/**
 * SEO Analysis Service
 * Database service for managing SEO analysis data in Supabase
 * Following the established patterns from headlineAnalysisService and designAnalysisService
 */

import { supabase, supabaseApi, getAuthenticatedApiClient } from '@/lib/supabase'
import type { 
  SEOAnalysis, 
  CreateSEOAnalysisData, 
  UpdateSEOAnalysisData,
  SEOAnalysisServiceResponse,
  SEOAnalysisStats
} from '@/types/seoAnalysisTypes'

class SEOAnalysisService {
  /**
   * Get authenticated API client with proper session
   */
  private async getApiClient() {
    return await getAuthenticatedApiClient();
  }

  /**
   * Save a new SEO analysis to the database
   */
  async saveAnalysis(analysisData: CreateSEOAnalysisData): Promise<SEOAnalysis> {
    try {
      console.log('💾 Saving SEO analysis:', {
        user_id: analysisData.user_id,
        url: analysisData.url?.substring(0, 50) + '...',
        overall_score: analysisData.overall_score,
        analysis_mode: analysisData.analysis_mode,
        status: analysisData.status
      });

      // Check authentication first
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user) {
        console.error('❌ Authentication error in saveAnalysis:', authError);
        throw new Error('User not authenticated');
      }

      console.log('✅ User authenticated for save:', {
        userId: user.id,
        email: user.email
      });

      // Check for duplicate analysis before saving
      if (analysisData.user_id && analysisData.url) {
        const existingAnalyses = await this.getUserAnalyses(analysisData.user_id, {
          limit: 5,
          orderBy: 'created_at',
          orderDirection: 'desc'
        });

        // Check if the same URL was analyzed recently (within last 5 minutes)
        const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
        const duplicateAnalysis = existingAnalyses.find(analysis => 
          analysis.url === analysisData.url &&
          analysis.analysis_mode === analysisData.analysis_mode &&
          new Date(analysis.created_at) > fiveMinutesAgo
        );

        if (duplicateAnalysis) {
          console.log('⚠️ Duplicate analysis detected, returning existing analysis');
          return duplicateAnalysis;
        }
      }

      // Ensure history limit: keep only last 9 analyses (so new one makes 10)
      await this.enforceHistoryLimit(analysisData.user_id);

      console.log('🔄 Inserting analysis data:', analysisData);

      const apiClient = await this.getApiClient();
      const { data, error } = await apiClient
        .from('seo_analyses')
        .insert(analysisData)
        .select()
        .single();

      if (error) {
        console.error('❌ Supabase insert error:', {
          error: error.message,
          code: error.code,
          details: error.details,
          hint: error.hint
        });
        throw new Error(`Failed to save analysis: ${error.message}`);
      }

      if (!data) {
        console.error('❌ No data returned from insert');
        throw new Error('No data returned from insert operation');
      }

      console.log('✅ SEO analysis saved successfully:', data.id);
      return data;
    } catch (error) {
      console.error('💥 Exception in saveAnalysis:', error);
      throw error;
    }
  }

  /**
   * Enforce history limit: keep only last 9 non-favorite analyses
   */
  private async enforceHistoryLimit(userId: string): Promise<void> {
    try {
      // Get non-favorite analyses ordered by creation date (newest first)
      const { data: analyses, error } = await supabaseApi
        .from('seo_analyses')
        .select('id, created_at')
        .eq('user_id', userId)
        .eq('is_favorite', false)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('❌ Error fetching analyses for history limit:', error);
        return;
      }

      // If we have 10 or more non-favorite analyses, delete the oldest ones
      if (analyses && analyses.length >= 10) {
        const analysesToDelete = analyses.slice(9); // Keep first 9, delete the rest
        const idsToDelete = analysesToDelete.map(a => a.id);

        if (idsToDelete.length > 0) {
          const { error: deleteError } = await supabaseApi
            .from('seo_analyses')
            .delete()
            .in('id', idsToDelete);

          if (deleteError) {
            console.error('❌ Error deleting old analyses:', deleteError);
          } else {
            console.log(`🗑️ Deleted ${idsToDelete.length} old analyses to maintain history limit`);
          }
        }
      }
    } catch (error) {
      console.error('💥 Exception in enforceHistoryLimit:', error);
      // Don't throw - this is a cleanup operation that shouldn't block saving
    }
  }

  /**
   * Get all SEO analyses for the current user
   */
  async getUserAnalyses(userId: string, options?: {
    limit?: number
    offset?: number
    toolType?: string
    isFavorite?: boolean
    orderBy?: 'created_at' | 'updated_at' | 'overall_score'
    orderDirection?: 'asc' | 'desc'
  }): Promise<SEOAnalysis[]> {
    try {
      console.log('🔍 getUserAnalyses called with:', { userId, options });

      // Validate user ID
      if (!userId || userId === 'anonymous') {
        console.warn('⚠️ Invalid user ID provided to getUserAnalyses');
        return [];
      }

      // Check authentication first
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError || !user) {
        console.error('❌ Authentication error in getUserAnalyses:', authError);
        throw new Error('User not authenticated');
      }

      if (user.id !== userId) {
        console.error('❌ User ID mismatch in getUserAnalyses:', { requestedUserId: userId, actualUserId: user.id });
        throw new Error('User ID mismatch');
      }

      // Build query with explicit column selection
      const apiClient = await this.getApiClient();
      let query = apiClient
        .from('seo_analyses')
        .select(`
          id,
          created_at,
          updated_at,
          user_id,
          url,
          analysis_mode,
          tool_type,
          analysis_version,
          overall_score,
          basic_info,
          content_analysis,
          seo_checks,
          recommendations,
          achievements,
          open_graph,
          twitter_card,
          preview_data,
          performance_metrics,
          analysis_duration_ms,
          status,
          error_message,
          ai_enhanced,
          is_favorite,
          custom_name,
          tags,
          notes,
          view_count,
          last_viewed_at,
          regeneration_count
        `)
        .eq('user_id', userId);

      // Apply filters
      if (options?.toolType) {
        query = query.eq('tool_type', options.toolType);
      }

      if (options?.isFavorite !== undefined) {
        query = query.eq('is_favorite', options.isFavorite);
      }

      // Apply ordering
      const orderBy = options?.orderBy || 'created_at';
      const orderDirection = options?.orderDirection || 'desc';
      query = query.order(orderBy, { ascending: orderDirection === 'asc' });

      // Apply pagination
      if (options?.limit) {
        query = query.limit(options.limit);
      }

      if (options?.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
      }

      console.log('🔍 Executing query for user analyses...');
      const { data, error } = await query;

      if (error) {
        console.error('❌ Error fetching user analyses:', {
          error: error.message,
          code: error.code,
          details: error.details,
          hint: error.hint
        });
        throw new Error(`Failed to fetch analyses: ${error.message}`);
      }

      console.log('✅ Successfully fetched analyses:', {
        count: data?.length || 0,
        userId,
        options
      });

      return data || [];
    } catch (error) {
      console.error('💥 Exception in getUserAnalyses:', error);
      throw error;
    }
  }

  /**
   * Get a specific SEO analysis by ID
   */
  async getAnalysisById(id: string): Promise<SEOAnalysis | null> {
    const { data, error } = await supabaseApi
      .from('seo_analyses')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null;
      }
      throw new Error(`Failed to fetch analysis: ${error.message}`);
    }

    return data;
  }

  /**
   * Update a SEO analysis
   */
  async updateAnalysis(updateData: UpdateSEOAnalysisData): Promise<SEOAnalysis> {
    const { id, ...updates } = updateData;

    const { data, error } = await supabaseApi
      .from('seo_analyses')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update analysis: ${error.message}`);
    }

    return data;
  }

  /**
   * Delete a SEO analysis
   */
  async deleteAnalysis(id: string): Promise<void> {
    const { error } = await supabaseApi
      .from('seo_analyses')
      .delete()
      .eq('id', id);

    if (error) {
      throw new Error(`Failed to delete analysis: ${error.message}`);
    }
  }

  /**
   * Get recent SEO analyses (last 10)
   */
  async getRecentAnalyses(): Promise<SEOAnalysis[]> {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      throw new Error('User not authenticated');
    }

    return this.getUserAnalyses(user.id, {
      limit: 10,
      orderBy: 'created_at',
      orderDirection: 'desc'
    });
  }

  /**
   * Get favorite SEO analyses
   */
  async getFavoriteAnalyses(): Promise<SEOAnalysis[]> {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      throw new Error('User not authenticated');
    }

    return this.getUserAnalyses(user.id, {
      isFavorite: true,
      orderBy: 'created_at',
      orderDirection: 'desc'
    });
  }

  /**
   * Toggle favorite status of an analysis
   */
  async toggleFavorite(id: string): Promise<SEOAnalysis> {
    // First get the current analysis
    const analysis = await this.getAnalysisById(id);
    if (!analysis) {
      throw new Error('Analysis not found');
    }

    // Toggle the favorite status
    return this.updateAnalysis({
      id,
      is_favorite: !analysis.is_favorite
    });
  }

  /**
   * Record a view for an analysis
   */
  async recordView(id: string): Promise<void> {
    try {
      const analysis = await this.getAnalysisById(id);
      if (!analysis) {
        console.warn('⚠️ Analysis not found for view recording:', id);
        return;
      }

      await this.updateAnalysis({
        id,
        view_count: (analysis.view_count || 0) + 1,
        last_viewed_at: new Date().toISOString()
      });

      console.log('👁️ View recorded for analysis:', id);
    } catch (error) {
      console.error('❌ Error recording view:', error);
      // Don't throw - view recording shouldn't break the app
    }
  }

  /**
   * Get user statistics
   */
  async getUserStats(userId: string): Promise<SEOAnalysisStats> {
    try {
      const analyses = await this.getUserAnalyses(userId);

      if (!analyses || analyses.length === 0) {
        console.log('📊 No analyses found for user stats');
        return {
          totalAnalyses: 0,
          favoriteAnalyses: 0,
          averageScore: 0,
          analysisTypeBreakdown: {},
          recentActivity: 0
        };
      }

      const totalAnalyses = analyses.length;
      const favoriteAnalyses = analyses.filter(a => a.is_favorite).length;
      const averageScore = analyses.reduce((sum, a) => sum + a.overall_score, 0) / totalAnalyses;

      // Analysis type breakdown
      const analysisTypeBreakdown: Record<string, number> = {};
      analyses.forEach(a => {
        const key = `${a.analysis_mode}_analysis`;
        analysisTypeBreakdown[key] = (analysisTypeBreakdown[key] || 0) + 1;
      });

      // Recent activity (analyses in last 7 days)
      const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      const recentActivity = analyses.filter(a =>
        new Date(a.created_at) > sevenDaysAgo
      ).length;

      return {
        totalAnalyses,
        favoriteAnalyses,
        averageScore: Math.round(averageScore * 10) / 10,
        analysisTypeBreakdown,
        recentActivity
      };
    } catch (error) {
      console.error('❌ Error getting user stats:', error);
      throw error;
    }
  }

  /**
   * Search analyses by URL or custom name
   */
  async searchAnalyses(userId: string, searchTerm: string): Promise<SEOAnalysis[]> {
    try {
      const { data, error } = await supabaseApi
        .from('seo_analyses')
        .select('*')
        .eq('user_id', userId)
        .or(`url.ilike.%${searchTerm}%,custom_name.ilike.%${searchTerm}%`)
        .order('created_at', { ascending: false });

      if (error) {
        throw new Error(`Search failed: ${error.message}`);
      }

      return data || [];
    } catch (error) {
      console.error('❌ Error searching analyses:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const seoAnalysisService = new SEOAnalysisService();
export default seoAnalysisService;
